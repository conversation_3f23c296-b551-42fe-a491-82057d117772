import { Modal, Form } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { useCallback, useState } from 'react';
import { type RootState, type AppDispatch } from '@/store';
import { useRegisterMutation } from '@/api/authApi';
import { closeModals, openLoginModal } from '@/store/slices/modalSlice';
import { CloseOutlined } from '@ant-design/icons';
import UserIcon from '@/assets/icons/user.svg?react';
import type { Dayjs } from 'dayjs';
import { stepFieldsMap } from '@/utils/validationRules';
import { useFormValidation } from '@/hooks/useFormValidation';
import { RegisterStep1 } from './RegisterStep1';
import { RegisterStep2 } from './RegisterStep2';
import { RegisterStep3 } from './RegisterStep3';
import { RegisterStep4 } from './RegisterStep4';
import { RegisterModalButtons } from './RegisterModalButtons';
import styles from './index.module.scss';
import { toast } from 'sonner';

import clsx from 'clsx';

import { AppButton } from '@/components';

interface RegisterFormData {
  username: string;
  name: string;
  surname: string;
  birthday: Dayjs;
  email: string;
  phone: string;
  password: string;
  privacyPolicy: boolean;
  termsConditions: boolean;
}

export const RegisterModal = () => {
  const dispatch = useDispatch<AppDispatch>();
  const [form] = Form.useForm();
  const [currentStep, setCurrentStep] = useState(1);
  const { registerModalOpen } = useSelector((state: RootState) => state.modal);
  const [register, { isLoading: isRegisterLoading }] = useRegisterMutation();

  const { isStepValid, checkCurrentStepValidity, setIsStepValid } = useFormValidation({
    form,
    currentStep,
    stepFieldsMap,
    enabled: registerModalOpen,
  });

  const resetFormAndState = useCallback(() => {
    setCurrentStep(1);
    setIsStepValid(false);

    if (registerModalOpen && form) {
      form.resetFields();
    }
  }, [setCurrentStep, setIsStepValid, registerModalOpen, form]);

  const handleCancel = useCallback(() => {
    dispatch(closeModals());
    resetFormAndState();
  }, [dispatch, resetFormAndState]);

  const STEP_INDICATOR_ARRAY = [
    {
      step: 1,
      key: 'step1',
      component: <RegisterStep1 />,
    },
    {
      step: 2,
      key: 'step2',
      component: <RegisterStep2 />,
    },
    {
      step: 3,
      key: 'step3',
      component: <RegisterStep3 />,
    },
    {
      step: 4,
      key: 'step4',
      component: <RegisterStep4 onCancel={handleCancel} />,
    },
  ];

  const handleSubmit = async (values: RegisterFormData) => {
    const allFormValues = form.getFieldsValue();
    console.log('All form data on submit:', allFormValues);

    const registerData = {
      name: `${values.name} ${values.surname}`,
      email: values.email,
      password: values.password,
    };

    try {
      await register(registerData).unwrap();
      toast.success('Registration successful! Please check your email for verification.');
      setCurrentStep(4);
    } catch (error: unknown) {
      const errorMessage =
        (error as { data?: { message?: string }; message?: string })?.data?.message ||
        (error as { message?: string })?.message ||
        'Registration failed';
      toast.error(errorMessage);
    }
  };

  const switchToLogin = () => {
    dispatch(openLoginModal());
    resetFormAndState();
  };

  const handleNext = async () => {
    const fieldsToCheck = [...(stepFieldsMap[currentStep as keyof typeof stepFieldsMap] || [])];
    try {
      await form.validateFields(fieldsToCheck);
      setCurrentStep((prev) => prev + 1);
      setIsStepValid(false);
    } catch (error) {
      // Validation failed, stay on current step
      console.log('Validation failed for step', currentStep, error);
    }
  };

  const handleBack = () => {
    setCurrentStep((prev) => prev - 1);
    setIsStepValid(false);
  };

  const renderStepIndicator = () => (
    <div className={styles.stepIndicator}>
      <p className={styles.text}>Step {currentStep} / 3</p>
      <div className={styles.bars}>
        {STEP_INDICATOR_ARRAY.map((step, i) => {
          if (i === 3) {
            return null;
          }
          return (
            <div
              key={step.key}
              className={`${styles.stepBar} ${step.step <= currentStep ? styles.active : styles.inactive}`}
            />
          );
        })}
      </div>
    </div>
  );

  return (
    <Modal
      open={registerModalOpen}
      onCancel={handleCancel}
      footer={null}
      centered
      transitionName=""
      maskTransitionName=""
      className={clsx(styles.register, 'register')}
      closeIcon={<CloseOutlined />}
    >
      <div className={styles.innerWrapper}>
        {currentStep !== 4 && (
          <div className={styles.header}>
            <UserIcon />
            <p className={styles.welcomeText}>Welcome to Liga Paryaj</p>
            <p className={styles.titleText}>Create your account</p>
          </div>
        )}

        {currentStep !== 4 && renderStepIndicator()}

        <Form
          form={form}
          name="registerForm"
          layout="vertical"
          onFinish={handleSubmit}
          autoComplete="off"
          preserve={false}
          onValuesChange={checkCurrentStepValidity}
          onFieldsChange={checkCurrentStepValidity}
        >
          {STEP_INDICATOR_ARRAY.map(({ step, key, component: StepComponent }) => (
            <div key={key} style={{ display: currentStep === step ? 'block' : 'none' }}>
              {StepComponent}
            </div>
          ))}

          {currentStep !== 4 && (
            <RegisterModalButtons
              currentStep={currentStep}
              totalSteps={3}
              isStepValid={isStepValid}
              isRegisterLoading={isRegisterLoading}
              onNext={handleNext}
              onBack={handleBack}
            />
          )}
        </Form>
      </div>

      {currentStep !== 4 && (
        <div className={styles.footer}>
          <span className={styles.footerText}>Already have an account? </span>
          <AppButton variant="link" onClick={switchToLogin}>
            Login
          </AppButton>
        </div>
      )}
    </Modal>
  );
};
