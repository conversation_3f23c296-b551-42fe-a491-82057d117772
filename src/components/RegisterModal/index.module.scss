@use '@/styles/media' as *;

.register {
  max-width: 480px;

  :global(.ant-modal-content) {
    padding: 0;
  }

  :global(.ant-modal-body) {
    @include mobile {
      height: 100vh;
      display: flex;
      flex-direction: column;
    }
  }

  @include mobile {
    max-width: 100vw;
    // Override the default inline width (set by Ant Design) to ensure full viewport usage
    width: 100vw !important;
    height: 100vh;
    margin-top: 0;
  }

  .innerWrapper {
    padding: 40px 40px 15px;

    @include mobile {
      padding: 25px 15px;
    }
  }

  .header {
    text-align: center;
    margin-bottom: 15px;

    .welcomeText {
      color: var(--neutral-300);
      font-size: var(--font-size-xs);
      margin-bottom: 4px;
      font-weight: var(--font-weight-semibold);
      margin-top: 15px;
    }

    .titleText {
      color: var(--white);
      font-size: var(--font-size-xxl);
      font-weight: var(--font-weight-bold);
      margin-bottom: 0;
    }
  }

  .birthday {
    margin-bottom: 10px;
  }

  .stepIndicator {
    margin-bottom: 20px;
    text-align: left;

    .text {
      font-size: var(--font-size-xs);
      color: var(--neutral-300);
      margin-bottom: 8px;
      font-weight: var(--font-weight-semibold);
    }

    .bars {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      .stepBar {
        height: 3px;
        border-radius: 2px;
        width: 30%;
        transition: all 0.3s ease;

        &.active {
          background-color: var(--primary-500);
        }

        &.inactive {
          background-color: var(--neutral-500);
        }
      }
    }
  }

  .footer {
    text-align: center;
    background: var(--neutral-600);
    color: var(--neutral-300);
    padding: 7px 0;

    @include mobile {
      margin-top: auto;
    }
  }
}

.buttons {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;

  :global(.ant-btn.default) {
    padding: 0 20px;
  }
}



.nextButton {
  margin-right: 0;
  margin-left: auto;
  max-width: max-content;
}

.verificationStep {
  text-align: center;
  padding: 5px 0;
  max-width: 400px;
  margin: 0 auto;

  .verificationCode {
    margin: 40px 0 70px;
  }

  .title {
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-bold);
    color: var(--white);
    margin: 15px 0;
  }

  .description {
    font-size: var(--font-size-sm);
    color: var(--neutral-300);

    .email {
      font-weight: var(--font-weight-bold);
    }
  }

  .errorText {
    color: var(--error);
    font-size: var(--font-size-xs);
    margin-top: 8px;
    text-align: left;
  }

  .actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 50px;

    & .primaryButton {
      width: auto;
    }
  }
}
