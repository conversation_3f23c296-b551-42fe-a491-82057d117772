@use '@/styles/media' as *;

.login {
  max-width: 480px;

  :global(.ant-modal-content) {
    padding: 0;
  }

  :global(.ant-modal-body) {
    @include mobile {
      height: 100vh;
      display: flex;
      flex-direction: column;
    }
  }

  @include mobile {
    max-width: 100vw;
    // Override the default inline width (set by Ant Design) to ensure full viewport usage
    width: 100vw !important;
    height: 100vh;
    margin-top: 0;
  }
}

.forgotPasswordWrapper,
.loginWrapper {
  .header {
    text-align: center;

    .title {
      color: var(--neutral-300);
      font-size: var(--font-size-xs);
      margin-bottom: 4px;
      font-weight: var(--font-weight-normal);
      font-size: var(--font-size-sm);
    }

    .text {
      color: var(--white);
      font-size: var(--font-size-xxl);
      font-weight: var(--font-weight-bold);
    }
  }

  :global(.ant-btn.default) {
    padding: 0 20px;
  }
}

.actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.loginButton {
  margin-bottom: 16px;
}

.forgotPassword {
  text-align: right;
  margin-bottom: 16px;
}

.email,
.password {
  label {
    cursor: pointer;
    color: var(--neutral-300);
    font-size: var(--font-size-xs);

    &::before {
      display: none;
    }
  }


  .loginWrapper {
    padding: 40px 40px 30px;

    @include mobile {
      padding: 25px 15px;
    }

    .actions {
      margin-top: 30px;
    }

    .header {
      margin-bottom: 25px;

      .title {
        margin-top: 25px;
      }

      .text {
        margin-top: 0;
      }
    }

    .forgotPasswordLink {
      padding: 0;
      font-size: 14px;
    }

    .footer {
      text-align: center;
      background: var(--neutral-600);
      color: var(--neutral-300);
      padding: 7px 0;

      @include mobile {
        margin-top: auto;
      }
    }
  }

  .forgotPasswordWrapper {
    padding: 40px 40px 55px;

    @include mobile {
      padding: 25px 15px;
    }

    .actions {
      margin-top: 70px;
    }

    .header {
      margin-bottom: 40px;

      .title {
        margin-top: 10px;
      }

      .text {
        margin-top: 35px;
      }
    }
  }

  .footer {
    text-align: center;
    background: var(--neutral-600);
    color: var(--neutral-300);
    padding: 7px 0;

    @include mobile {
      margin-top: auto;
    }
  }
}
