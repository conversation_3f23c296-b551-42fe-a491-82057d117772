import {
  UserOutlined,
  LogoutOutlined,
  WalletOutlined,
  SettingOutlined,
  QuestionCircleOutlined,
  FileTextOutlined,
  TransactionOutlined,
} from '@ant-design/icons';

// Import SVG icons for header and footer
import SportsIcon from '@/assets/icons/footerNavigation/sports-footer-nav.svg?react';
import LiveSportsIcon from '@/assets/icons/headerNavigation/live-sports-top-navigation.svg?react';
import CasinoIcon from '@/assets/icons/headerNavigation/casino-top-navigation.svg?react';
import LiveCasinoIcon from '@/assets/icons/headerNavigation/live-casion-top-navigation.svg?react';
import VirtualIcon from '@/assets/icons/headerNavigation/virtual-top-navigation.svg?react';
import PromotionsIcon from '@/assets/icons/headerNavigation/promotion-top-navigation.svg?react';
import BetsIcon from '@/assets/icons/footerNavigation/bets-footer-nav.svg?react';
import WalletIcon from '@/assets/icons/wallet.svg?react';
import SettingsIcon from '@/assets/icons/footerNavigation/settings-footer-nav.svg?react';
import LiveGamesIcon from '@/assets/icons/live-games.svg?react';
import UpcomingIcon from '@/assets/icons/upcoming-nav.svg?react';
import FavoritesIcon from '@/assets/icons/star-outlined.svg?react';
import BrowseIcon from '@/assets/icons/footerNavigation/browse-footer-nav.svg?react';
import HomeIcon from '@/assets/icons/home-nav.svg?react';

// Flag icons
import EnFlagIcon from '@/assets/icons/flags/en.svg?react';
import EsFlagIcon from '@/assets/icons/flags/es.svg?react';
import FrFlagIcon from '@/assets/icons/flags/fr.svg?react';

// Sport icons imports
import BasketballIcon from '@/assets/icons/sports/basketball-mini.png';
import IceHockeyIcon from '@/assets/icons/sports/hockey-mini.png';
import TennisIcon from '@/assets/icons/sports/tennis-mini.png';
import HandballIcon from '@/assets/icons/sports/handball-mini.png';
import AmericanFootballIcon from '@/assets/icons/sports/american-football-mini.png';
import BaseballIcon from '@/assets/icons/sports/baseball-mini.png';
import TableTennisIcon from '@/assets/icons/sports/table-tennis-mini.png';
import CricketIcon from '@/assets/icons/sports/cricket-mini.png';
import VolleyballIcon from '@/assets/icons/sports/volleyball-mini.png';
import EsportsIcon from '@/assets/icons/sports/esports-mini.png';
import FootballIcon from '@/assets/icons/sports/football-mini.png';

import { ODD_TYPE, SportType } from '@/types/entities.ts';
import type { NotificationSettings } from '@/api/userApi';

// Responsive breakpoints
export const MOBILE_WIDTH_BREAKPOINT = 768;
export const LARGE_WIDTH_BREAKPOINT = 1024;
export const LAPTOP_WIDTH_BREAKPOINT = 1440;

// Router paths
export enum ROUTER_PATHS {
  HOME = '/',
  SPORTS = '/sports',
  BETS = '/bets',
  CASINO = '/casino',
  SETTINGS = '/settings',
  PROFILE = '/profile',
  WALLET = '/wallet',
  BROWSE = '/browse',
  TRANSACTIONS = '/transactions',
  FAQ_HELP = '/faq-help',
  LEGAL_PAGES = '/legal-pages',
  TERMS = '/terms',
  PRIVACY = '/privacy',
  RESPONSIBLE_GAMING = '/responsible-gaming',
  NOT_FOUND = '/not-found',
  NO_MATCH = '*',
}

// Language options
export const LANGUAGE_OPTIONS = [
  { value: 'en', label: 'English', flag: EnFlagIcon },
  { value: 'es', label: 'Español', flag: EsFlagIcon },
  { value: 'fr', label: 'Français', flag: FrFlagIcon },
];

// Header menu items
export const HEADER_MENU_ITEMS = [
  { key: 'sports', label: 'Sports', path: ROUTER_PATHS.SPORTS, icon: SportsIcon },
  { key: 'live-sports', label: 'Live Sports', path: '/live-sports', icon: LiveSportsIcon },
  { key: 'casino', label: 'Casino', path: ROUTER_PATHS.CASINO, icon: CasinoIcon },
  { key: 'live-casino', label: 'Live Casino', path: '/live-casino', icon: LiveCasinoIcon },
  { key: 'virtual', label: 'Virtual', path: '/virtual', icon: VirtualIcon },
  { key: 'promotions', label: 'Promotions', path: '/promotions', icon: PromotionsIcon },
];

export const oddsFormatOptions = [
  {
    value: 'decimal',
    label: 'Decimal',
  },
  {
    value: 'fractional',
    label: 'Fractional',
  },
  {
    value: 'American',
    label: 'american',
  },
];

// User profile menu items
export const USER_PROFILE_MENU_ITEMS = [
  {
    key: 'profile',
    icon: UserOutlined,
    label: 'Profile',
    path: ROUTER_PATHS.PROFILE,
  },
  {
    key: 'wallet',
    icon: WalletOutlined,
    label: 'Wallet',
    path: ROUTER_PATHS.WALLET,
  },
  {
    key: 'transactions',
    icon: TransactionOutlined,
    label: 'Transactions',
    path: ROUTER_PATHS.TRANSACTIONS,
  },
  {
    key: 'settings',
    icon: SettingOutlined,
    label: 'Settings',
    path: ROUTER_PATHS.SETTINGS,
  },
  {
    key: 'faq-help',
    icon: QuestionCircleOutlined,
    label: 'FAQ/Help',
    path: ROUTER_PATHS.FAQ_HELP,
  },
  {
    key: 'legal-pages',
    icon: FileTextOutlined,
    label: 'Legal Pages',
    path: ROUTER_PATHS.LEGAL_PAGES,
  },
  {
    type: 'divider' as const,
  },
  {
    key: 'logout',
    icon: LogoutOutlined,
    label: 'Logout',
    isAction: true,
  },
];

// Account navigation menu items
export const ACCOUNT_NAV_MENU_ITEMS = [
  { key: 'wallet', label: 'Wallet', path: ROUTER_PATHS.WALLET, icon: WalletIcon },
  { key: 'settings', label: 'Settings', path: ROUTER_PATHS.SETTINGS, icon: SettingsIcon },
  {
    key: 'transactions',
    label: 'Transactions',
    path: ROUTER_PATHS.TRANSACTIONS,
    icon: TransactionOutlined,
  },
  { key: 'faq-help', label: 'FAQ/Help', path: ROUTER_PATHS.FAQ_HELP, icon: QuestionCircleOutlined },
  {
    key: 'legal-pages',
    label: 'Legal Pages',
    path: ROUTER_PATHS.LEGAL_PAGES,
    icon: FileTextOutlined,
  },
  { key: 'logout', label: 'Logout', isAction: true, icon: LogoutOutlined },
];

// Footer navigation menu items
export const FOOTER_MENU_ITEMS = [
  { key: 'sports', label: 'Sports', path: ROUTER_PATHS.SPORTS, icon: SportsIcon },
  { key: 'bets', label: 'My Bets', path: ROUTER_PATHS.BETS, icon: BetsIcon },
  { key: 'casino', label: 'Casino', path: ROUTER_PATHS.CASINO, icon: CasinoIcon },
  { key: 'settings', label: 'Settings', path: ROUTER_PATHS.SETTINGS, icon: SettingsIcon },
  { key: 'browse', label: 'Browse', path: ROUTER_PATHS.BROWSE, icon: BrowseIcon },
];

// Sidebar navigation menu items
export const SIDEBAR_NAV_MENU_ITEMS = [
  { key: 'home', label: 'Home', path: ROUTER_PATHS.HOME, icon: HomeIcon },
  { key: 'live-matches', label: 'Live matches', path: '/live-matches', icon: LiveGamesIcon },
  { key: 'upcoming', label: 'Upcoming', path: '/upcoming', icon: UpcomingIcon },
  { key: 'favorites', label: 'Favorites', path: '/favorites', icon: FavoritesIcon },
  { key: 'my-bets', label: 'My bets', path: ROUTER_PATHS.BETS, icon: BetsIcon },
];

// Settings tabs
export const SETTINGS_TABS = [
  { key: 'personal-info', label: 'Personal Info' },
  { key: 'verification', label: 'Verification' },
  { key: 'games-limit', label: 'Games Limit' },
  { key: 'multiple-logins', label: 'Multiple Logins' },
  { key: 'account-deactivation', label: 'Account Deactivation' },
  { key: 'notification-settings', label: 'Notification Settings' },
];

// Age confirmation configuration
export const AGE_CONFIRMATION_CONFIG = {
  STORAGE_KEY: 'ageConfirmationAccepted',
  MIN_AGE: 18,
  LINKS: {
    TERMS: ROUTER_PATHS.TERMS,
    PRIVACY: ROUTER_PATHS.PRIVACY,
    RESPONSIBLE_GAMING: ROUTER_PATHS.RESPONSIBLE_GAMING,
  },
} as const;

// Breadcrumb route labels
export const BREADCRUMB_LABELS: Record<string, string> = {
  sports: 'Sports',
  bets: 'My Bets',
  casino: 'Casino',
  settings: 'Settings',
  profile: 'Profile',
  'personal-info': 'Personal Info',
  verification: 'Verification',
  'games-limit': 'Games Limit',
  'multiple-logins': 'Multiple Logins',
  'account-deactivation': 'Account Deactivation',
  'notification-settings': 'Notification Settings',
  wallet: 'Wallet',
  transactions: 'Transactions',
  'faq-help': 'FAQ/Help',
  'legal-pages': 'Legal Pages',
  logout: 'Logout',
};

export interface CountryOption {
  value: string;
  label: string;
}

export const ACCESS_TOKEN_NAME = 'accessToken';
export const REFRESH_TOKEN_NAME = 'refreshToken';

export const GROUPED_ODDS_SPORTS: Partial<
  Record<
    SportType,
    {
      tablet: string[];
      mobile: string[];
    }
  >
> = {
  [SportType.FOOTBALL]: {
    tablet: ['1x2', 'OVER_TOTAL'],
    mobile: ['1x2', 'Over/Under', 'Handicap'],
  },
};

export const ODD_GROUPS_MAP: Record<string, ODD_TYPE[]> = {
  '1x2': [ODD_TYPE.ONE, ODD_TYPE.DRAW, ODD_TYPE.TWO],
  OVER_TOTAL: [ODD_TYPE.OVER, ODD_TYPE.TOTAL, ODD_TYPE.UNDER],
  ALL: [ODD_TYPE.ONE, ODD_TYPE.DRAW, ODD_TYPE.TWO, ODD_TYPE.UNDER, ODD_TYPE.OVER, ODD_TYPE.TOTAL],
  'Over/Under': [ODD_TYPE.OVER, ODD_TYPE.UNDER],
};

export const ODD_TYPE_TITLES: Record<ODD_TYPE, string> = {
  [ODD_TYPE.ONE]: '1',
  [ODD_TYPE.DRAW]: 'X',
  [ODD_TYPE.TWO]: '2',
  [ODD_TYPE.OVER]: 'Over',
  [ODD_TYPE.UNDER]: 'Under',
  [ODD_TYPE.TOTAL]: 'Total',
};

export const GAMES_SORTING_ORDER_OPTIONS = [
  {
    value: 'date',
    label: 'Date',
  },
  {
    value: 'competition',
    label: 'Competition',
  },
];

export const SPORT_ICON_MAP: Record<SportType, string> = {
  [SportType.BASKETBALL]: BasketballIcon,
  [SportType.ICE_HOCKEY]: IceHockeyIcon,
  [SportType.TENNIS]: TennisIcon,
  [SportType.HANDBALL]: HandballIcon,
  [SportType.AMERICAN_FOOTBALL]: AmericanFootballIcon,
  [SportType.BASEBALL]: BaseballIcon,
  [SportType.TABLE_TENNIS]: TableTennisIcon,
  [SportType.CRICKET]: CricketIcon,
  [SportType.VOLLEYBALL]: VolleyballIcon,
  [SportType.ESPORTS]: EsportsIcon,
  [SportType.FOOTBALL]: FootballIcon,
};

export const COUNTRY_OPTIONS: CountryOption[] = [
  { value: 'DE', label: '🇩🇪 DE' },
  { value: 'US', label: '🇺🇸 US' },
  { value: 'UK', label: '🇬🇧 UK' },
  { value: 'FR', label: '🇫🇷 FR' },
  { value: 'ES', label: '🇪🇸 ES' },
  { value: 'IT', label: '🇮🇹 IT' },
  { value: 'NL', label: '🇳🇱 NL' },
  { value: 'PL', label: '🇵🇱 PL' },
  { value: 'RU', label: '🇷🇺 RU' },
  { value: 'UA', label: '🇺🇦 UA' },
];

export const ODDS_FORMAT_OPTIONS = [
  {
    value: 'decimal',
    label: 'Decimal',
  },
  {
    value: 'fractional',
    label: 'Fractional',
  },
  {
    value: 'american',
    label: 'American',
  },
];

// Notification settings options
export const NOTIFICATION_OPTIONS: Array<{
  key: keyof NotificationSettings;
  title: string;
  description: string;
}> = [
  {
    key: 'emailMarketing',
    title: 'Email marketing',
    description: 'Get updates on promotions and news directly to your email.',
  },
  {
    key: 'bonusNotifications',
    title: 'Bonus notifications',
    description: 'Receive alerts when bonuses are available or about to expire.',
  },
  {
    key: 'winNotifications',
    title: 'Win notifications',
    description: 'Celebrate your victories with instant updates on your wins.',
  },
  {
    key: 'depositNotifications',
    title: 'Deposit notifications',
    description: 'Receive confirmation when your deposit is successful.',
  },
  {
    key: 'withdrawalNotifications',
    title: 'Withdrawal notifications',
    description: 'Get updates when your withdrawal is processed.',
  },
];

export const FOOTER_COLUMNS: { title: string; items: string[] }[] = [
  {
    title: 'Main',
    items: ['Casino', 'Sports', 'VIP', 'Affiliate Program', 'Promotions', 'Bonuses', 'Tournaments'],
  },
  {
    title: 'Sports',
    items: [
      'Sportsbook',
      'Live Sports',
      'Soccer',
      'Basketball',
      'American Football',
      'Baseball',
      'Tennis',
      'Table Tennis',
    ],
  },
  { title: 'Casino', items: ['Slots', 'Jackpots', 'Table Games', 'Other games'] },
  {
    title: 'Live Casino',
    items: ['Game Shows', 'Blackjack', 'Roulette', 'Baccarat', 'Poker', 'Other games'],
  },
  {
    title: 'Information',
    items: [
      'Responsible Gaming',
      'Bonuses & Promotions Conditions',
      'Terms & Conditions',
      'Payment Methods',
      'FAQ/Help',
      'Privacy Policy',
    ],
  },
  { title: 'Help', items: ['About', 'Support'] },
];

// Multiple Logins constants
export const DEVICE_TYPES = {
  DESKTOP: 'desktop',
  MOBILE: 'mobile',
  TABLET: 'tablet',
  UNKNOWN: 'unknown',
} as const;

export const SESSION_STATUS = {
  ACTIVE: 'active',
  NOT_ACTIVE: 'not_active',
} as const;

// Device type detection patterns
export const DEVICE_PATTERNS = {
  WINDOWS: /Windows/i,
  MAC: /Mac|macOS/i,
  LINUX: /Linux/i,
  ANDROID: /Android/i,
  IOS: /iPhone|iPad|iPod/i,
  CHROME: /Chrome/i,
  FIREFOX: /Firefox/i,
  SAFARI: /Safari/i,
  EDGE: /Edge/i,
} as const;

// Mock data for Multiple Logins (temporary)
export const MOCK_LOGIN_SESSIONS = [
  {
    id: '1',
    deviceInfo: 'PC 64 bit, Windows 10',
    ipAddress: '**************',
    status: 'active' as const,
    lastActive: new Date().toISOString(),
    location: 'Ukraine',
    browser: 'Chrome',
    os: 'Windows 10',
  },
  {
    id: '2',
    deviceInfo: 'iPhone 12 Pro, iOS, 17.6.1',
    ipAddress: '*************',
    status: 'not_active' as const,
    lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    location: 'Ukraine',
    browser: 'Safari',
    os: 'iOS 17.6.1',
  },
  {
    id: '3',
    deviceInfo: 'MacBook Pro, macOS 14.3',
    ipAddress: '************',
    status: 'not_active' as const,
    lastActive: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
    location: 'Ukraine',
    browser: 'Safari',
    os: 'macOS 14.3',
  },
  {
    id: '4',
    deviceInfo: 'iPhone 12 Pro, iOS, 17.6.1',
    ipAddress: '*************',
    status: 'not_active' as const,
    lastActive: new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString(), // 2 days ago
    location: 'Ukraine',
    browser: 'Safari',
    os: 'iOS 17.6.1',
  },
  {
    id: '5',
    deviceInfo: 'PC 64 bit, Windows 10',
    ipAddress: '**************',
    status: 'not_active' as const,
    lastActive: new Date(Date.now() - 72 * 60 * 60 * 1000).toISOString(), // 3 days ago
    location: 'Ukraine',
    browser: 'Chrome',
    os: 'Windows 10',
  },
];
