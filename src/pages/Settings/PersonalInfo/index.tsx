import { Form, Input, DatePicker, Collapse } from 'antd';
import { useSelector, useDispatch } from 'react-redux';
import { useState } from 'react';
import { type RootState, type AppDispatch } from '@/store';
import { useUpdateProfileMutation, useChangePasswordMutation } from '@/api/userApi';
import { setUser } from '@/store/slices/authSlice';
import dayjs from 'dayjs';
import styles from './PersonalInfo.module.scss';
import { toast } from 'sonner';

import { AppButton } from '@/components/AppButton';
import { PasswordValidator } from '@/components/PasswordValidator';
import { validationRules } from '@/utils/validationRules';

interface PersonalInfoFormData {
  name: string;
  surname: string;
  username: string;
  birthday: dayjs.Dayjs;
}

interface ChangePasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export const PersonalInfo = () => {
  const [form] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const [updateProfile, { isLoading: isUpdateProfileLoading }] = useUpdateProfileMutation();
  const [changePassword, { isLoading: isChangePasswordLoading }] = useChangePasswordMutation();

  const [isPasswordCollapseOpen, setIsPasswordCollapseOpen] = useState(false);

  const handleSubmit = async (values: PersonalInfoFormData) => {
    try {
      const updatedUser = await updateProfile({
        name: values.name,
        surname: values.surname,
        username: values.username,
        birthday: values.birthday.format('YYYY-MM-DD'),
      }).unwrap();

      dispatch(setUser(updatedUser));

      toast.success('Profile updated successfully');
    } catch (error: unknown) {
      const errorMessage =
        (error as { data?: { message?: string }; message?: string })?.data?.message ||
        (error as { message?: string })?.message ||
        'Failed to update profile';

      toast.error(errorMessage);
      console.error('Update profile error:', error);
    }
  };

  const handlePasswordSubmit = async (values: ChangePasswordFormData) => {
    try {
      await changePassword({
        currentPassword: values.currentPassword,
        newPassword: values.newPassword,
        confirmPassword: values.confirmPassword,
      }).unwrap();

      toast.success('Password changed successfully');
      passwordForm.resetFields();
      setIsPasswordCollapseOpen(false);
    } catch (error: unknown) {
      const errorMessage =
        (error as { data?: { message?: string }; message?: string })?.data?.message ||
        (error as { message?: string })?.message ||
        'Failed to change password';

      toast.error(errorMessage);
      console.error('Change password error:', error);
    }
  };

  const handleChangePasswordClick = () => {
    setIsPasswordCollapseOpen(!isPasswordCollapseOpen);
  };

  return (
    <>
      <div className={styles.personalInfo}>
        <div className={styles.innerWrapper}>
          <div className={styles.header}>
            <h2 className={styles.title}>Profile info</h2>
            <AppButton
              variant="outlined"
              className={styles.saveButton}
              htmlType="submit"
              form="profile-form"
              loading={isUpdateProfileLoading}
            >
              {isUpdateProfileLoading ? 'Saving...' : 'Edit'}
            </AppButton>
          </div>

          <Form
            id="profile-form"
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={{
              name: user?.name || '',
              surname: user?.surname || '',
              username: user?.username || '',
              birthday: user?.birthday ? dayjs(user.birthday) : undefined,
            }}
            className={styles.form}
          >
            <div className={styles.formRow}>
              <Form.Item
                label="Name"
                name="name"
                className={styles.formItem}
                rules={[{ required: true, message: 'Please enter your name' }]}
              >
                <Input placeholder={user?.name || 'Enter your name'} className={styles.input} />
              </Form.Item>

              <Form.Item
                label="Surname"
                name="surname"
                className={styles.formItem}
                rules={[{ required: true, message: 'Please enter your surname' }]}
              >
                <Input
                  placeholder={user?.surname || 'Enter your surname'}
                  className={styles.input}
                />
              </Form.Item>
            </div>

            <div className={styles.formRow}>
              <Form.Item
                label="Username"
                name="username"
                className={styles.formItem}
                rules={[{ required: true, message: 'Please enter your username' }]}
              >
                <Input
                  placeholder={user?.username || 'Enter your username'}
                  className={styles.input}
                />
              </Form.Item>

              <Form.Item
                label="Birthday"
                name="birthday"
                className={styles.formItem}
                rules={[{ required: true, message: 'Please select your birthday' }]}
              >
                <DatePicker
                  placeholder={
                    user?.birthday
                      ? dayjs(user.birthday).format('DD.MM.YYYY')
                      : 'Select your birthday'
                  }
                  className={styles.input}
                  format="DD.MM.YYYY"
                />
              </Form.Item>
            </div>
          </Form>
        </div>

        <div className={styles.innerWrapper}>
          <div className={styles.passwordSection}>
            <div className={styles.passwordHeader}>
              <h2 className={styles.passwordTitle}>Password</h2>
              <AppButton
                variant="outlined"
                className={styles.changePasswordButton}
                onClick={handleChangePasswordClick}
              >
                Change password
              </AppButton>
            </div>

            <Collapse
              activeKey={isPasswordCollapseOpen ? ['password'] : []}
              ghost
              className={styles.passwordCollapse}
            >
              <Collapse.Panel key="password" header="" showArrow={false}>
                <Form
                  form={passwordForm}
                  layout="vertical"
                  onFinish={handlePasswordSubmit}
                  autoComplete="off"
                  className={styles.passwordForm}
                >
                  <Form.Item
                    name="currentPassword"
                    label="Current Password"
                    rules={[{ required: true, message: 'Please enter your current password' }]}
                    className={styles.formItem}
                  >
                    <Input.Password placeholder="Enter current password" />
                  </Form.Item>

                  <PasswordValidator
                    name="newPassword"
                    placeholder="Enter new password"
                    label="New Password"
                    className={styles.formItem}
                  />

                  <Form.Item
                    name="confirmPassword"
                    label="Repeat Password"
                    dependencies={['newPassword']}
                    rules={validationRules.confirmPassword((name: string) =>
                      passwordForm.getFieldValue(name),
                    )}
                    className={styles.formItem}
                  >
                    <Input.Password placeholder="Repeat password" />
                  </Form.Item>

                  <div className={styles.passwordFormActions}>
                    <AppButton
                      variant="outlined"
                      onClick={() => {
                        passwordForm.resetFields();
                        setIsPasswordCollapseOpen(false);
                      }}
                      disabled={isChangePasswordLoading}
                    >
                      Cancel
                    </AppButton>
                    <AppButton
                      variant="primary"
                      htmlType="submit"
                      loading={isChangePasswordLoading}
                      disabled={isChangePasswordLoading}
                    >
                      {isChangePasswordLoading ? 'Changing...' : 'Change Password'}
                    </AppButton>
                  </div>
                </Form>
              </Collapse.Panel>
            </Collapse>
          </div>
        </div>
      </div>
    </>
  );
};
