@use '@/styles/media' as *;

.personalInfo {
  h2 {
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-semibold);
  }

  .innerWrapper {
    background-color: var(--neutral-600);
    border-radius: 8px;
  }

  > * {
    padding: 0 15px;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-top: 15px;

    @include mobile {
      margin-bottom: 24px;
    }
  }

  .title {
    color: var(--white);
  }

  .saveButton {
    min-width: 80px;
  }

  .formRow {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 14px;
  }

  .form {
    padding-bottom: 20px;
  }

  label {
    cursor: pointer;
    color: var(--neutral-300);
    font-size: var(--font-size-xs);

    &::before {
      display: none;
    }
  }

  .formItem {
    margin-bottom: 20px;
  }

  .passwordSection {
    margin-top: 10px;
    padding: 15px 0;
  }

  .passwordHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .passwordTitle {
    color: var(--white);
  }

  .changePasswordButton {
    min-width: 140px;

    @include mobile {
      min-width: 120px;
      font-size: var(--font-size-xs);
    }
  }

  .passwordCollapse {
    margin-top: 20px;

    :global(.ant-collapse-item) {
      border: none !important;
      background: transparent !important;
    }

    :global(.ant-collapse-header) {
      display: none !important;
    }

    :global(.ant-collapse-content) {
      border: none !important;
      background: transparent !important;
    }

    :global(.ant-collapse-content-box) {
      padding: 0 !important;
    }
  }

  .passwordForm {
    padding-top: 10px;

    .formItem {
      margin-bottom: 20px;
    }
  }

  .passwordFormActions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid var(--neutral-500);

    @include mobile {
      flex-direction: column;
      gap: 8px;
    }
  }
}
