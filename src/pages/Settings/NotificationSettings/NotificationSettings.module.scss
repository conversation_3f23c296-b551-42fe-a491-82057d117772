@use '@/styles/media' as *;

.notificationSettings {
  .innerWrapper {
    background-color: var(--neutral-600);
    border-radius: 8px;
    padding: 0 15px;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2px;
    padding-top: 15px;
  }

  .title {
    color: var(--white);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-semibold);
    margin: 0;
  }

  .settingsList {
    padding-bottom: 20px;
    max-width: 60%;
  }

  .settingItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 34px 0 9px;
    border-bottom: 1px solid var(--neutral-500);

    @include mobile {
      align-items: center;
    }
  }

  .settingContent {
    flex: 1;
    margin-right: 20px;

    @include mobile {
      margin-right: 0;
    }
  }

  .settingTitle {
    color: var(--white);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    margin: 0 0 6px 0;
  }

  .settingDescription {
    color: var(--neutral-300);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-normal);
    margin: 0;
  }

  .switchWrapper {
    flex-shrink: 0;
    display: flex;
    align-items: center;
  }
}

.skeleton {
  .header {
    span {
      width: 200px;
      height: 32px;
    }
  }

  .settingContent {
    & > div {
      width: 100%;
    }

    span {
      width: 100%;
      height: 50px;
    }
  }

  .switchWrapper {
    span {
      width: 44px;
      height: 22px;
    }
  }
}
